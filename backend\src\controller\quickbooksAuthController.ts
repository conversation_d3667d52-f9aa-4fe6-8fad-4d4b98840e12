// controllers/quickbooksAuthController.ts

import { Request, Response } from 'express';
import { sendSuccess, sendError } from '../utils/responseHandler';
import { QBOAuthRequest, QBODisconnectRequest } from '../types/quickbooks';
import { QuickBooksAuthService } from '../service/quickbooksAuthService';

export class QuickBooksAuthController {
  private authService: QuickBooksAuthService;

  constructor() {
    this.authService = new QuickBooksAuthService();
  }

  /**
   * Initialize QuickBooks OAuth flow
   * GET /api/auth/quickbooks/connect
   */
  public connect = async (req: Request, res: Response): Promise<Response> => {
    try {
      const { returnUrl } = req.query as { returnUrl?: string };
      
      const { authUrl, state } = this.authService.generateAuthUrl(returnUrl);

      return sendSuccess(res, 'QuickBooks authorization URL generated successfully', {
        authUrl,
        state,
        message: 'Redirect user to this URL to begin QuickBooks authentication'
      });

    } catch (error) {
      console.error('Error generating QuickBooks auth URL:', error);
      return sendError(
        res, 
        'Failed to generate QuickBooks authorization URL', 
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };

  /**
   * Handle OAuth callback from QuickBooks
   * GET /api/auth/quickbooks/callback
   */
  public callback = async (req: Request, res: Response): Promise<Response> => {
    try {
      const { code, state, realmId } = req.query;

      // Validate required parameters
      if (!code || !state || !realmId) {
        return sendError(res, 'Missing required OAuth parameters', {
          missing: {
            code: !code,
            state: !state,
            realmId: !realmId
          }
        }, 400);
      }

      const authRequest: QBOAuthRequest = {
        code: code as string,
        state: state as string,
        realmId: realmId as string
      };

      const connection = await this.authService.handleCallback(authRequest);

      return sendSuccess(res, 'QuickBooks connection established successfully', {
        connection: {
          id: connection.id,
          realmId: connection.realmId,
          companyName: connection.companyName,
          isConnected: connection.isConnected,
          connectedAt: connection.connectedAt,
          expiresAt: connection.expiresAt
        },
        message: 'You are now connected to QuickBooks. You can start syncing your data.'
      }, 201);

    } catch (error) {
      console.error('Error handling QuickBooks OAuth callback:', error);
      return sendError(
        res,
        'Failed to establish QuickBooks connection',
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };

  /**
   * Refresh access token
   * POST /api/auth/quickbooks/refresh
   */
  public refresh = async (req: Request, res: Response): Promise<Response> => {
    try {
      const { connectionId } = req.body;

      if (!connectionId) {
        return sendError(res, 'Connection ID is required', null, 400);
      }

      const connection = await this.authService.refreshToken(connectionId);

      return sendSuccess(res, 'Access token refreshed successfully', {
        connection: {
          id: connection.id,
          realmId: connection.realmId,
          companyName: connection.companyName,
          expiresAt: connection.expiresAt,
          refreshExpiresAt: connection.refreshExpiresAt
        }
      });

    } catch (error) {
      console.error('Error refreshing QuickBooks token:', error);
      return sendError(
        res,
        'Failed to refresh access token',
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };

  /**
   * Disconnect and revoke QuickBooks tokens
   * POST /api/auth/quickbooks/disconnect
   */
  public disconnect = async (req: Request, res: Response): Promise<Response> => {
    try {
      const { connectionId }: QBODisconnectRequest = req.body;

      if (!connectionId) {
        return sendError(res, 'Connection ID is required', null, 400);
      }

      await this.authService.disconnect(connectionId);

      return sendSuccess(res, 'Successfully disconnected from QuickBooks', {
        connectionId,
        message: 'QuickBooks connection has been revoked. All tokens have been invalidated.'
      });

    } catch (error) {
      console.error('Error disconnecting from QuickBooks:', error);
      return sendError(
        res,
        'Failed to disconnect from QuickBooks',
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };

  /**
   * Get connection status
   * GET /api/auth/quickbooks/status/:connectionId
   */
  public getConnectionStatus = async (req: Request, res: Response): Promise<Response> => {
    try {
      const { connectionId } = req.params;

      if (!connectionId) {
        return sendError(res, 'Connection ID is required', null, 400);
      }

      const connection = await this.authService.getConnection(connectionId);

      if (!connection) {
        return sendError(res, 'Connection not found', null, 404);
      }

      // Check if token is expired and needs refresh
      const now = new Date();
      const isTokenExpired = now >= connection.expiresAt;
      const isRefreshTokenExpired = now >= connection.refreshExpiresAt;

      return sendSuccess(res, 'Connection status retrieved successfully', {
        connection: {
          id: connection.id,
          realmId: connection.realmId,
          companyName: connection.companyName,
          isConnected: connection.isConnected,
          connectedAt: connection.connectedAt,
          lastSyncAt: connection.lastSyncAt,
          expiresAt: connection.expiresAt,
          refreshExpiresAt: connection.refreshExpiresAt
        },
        tokenStatus: {
          isTokenExpired,
          isRefreshTokenExpired,
          needsRefresh: isTokenExpired && !isRefreshTokenExpired,
          needsReauth: isRefreshTokenExpired
        }
      });

    } catch (error) {
      console.error('Error getting connection status:', error);
      return sendError(
        res,
        'Failed to get connection status',
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };

  /**
   * Get all active connections
   * GET /api/auth/quickbooks/connections
   */
  public getConnections = async (req: Request, res: Response): Promise<Response> => {
    try {
      const connections = await this.authService.getActiveConnections();

      const connectionsWithStatus = connections.map(connection => {
        const now = new Date();
        const isTokenExpired = now >= connection.expiresAt;
        const isRefreshTokenExpired = now >= connection.refreshExpiresAt;

        return {
          id: connection.id,
          realmId: connection.realmId,
          companyName: connection.companyName,
          isConnected: connection.isConnected,
          connectedAt: connection.connectedAt,
          lastSyncAt: connection.lastSyncAt,
          expiresAt: connection.expiresAt,
          tokenStatus: {
            isTokenExpired,
            isRefreshTokenExpired,
            needsRefresh: isTokenExpired && !isRefreshTokenExpired,
            needsReauth: isRefreshTokenExpired
          }
        };
      });

      return sendSuccess(res, 'Active connections retrieved successfully', {
        connections: connectionsWithStatus,
        totalConnections: connectionsWithStatus.length
      });

    } catch (error) {
      console.error('Error getting active connections:', error);
      return sendError(
        res,
        'Failed to get active connections',
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };

  /**
   * Test connection by making a simple API call
   * POST /api/auth/quickbooks/test/:connectionId
   */
  public testConnection = async (req: Request, res: Response): Promise<Response> => {
    try {
      const { connectionId } = req.params;

      if (!connectionId) {
        return sendError(res, 'Connection ID is required', null, 400);
      }

      const connection = await this.authService.getConnection(connectionId);

      if (!connection) {
        return sendError(res, 'Connection not found', null, 404);
      }

      if (!connection.isConnected) {
        return sendError(res, 'Connection is not active', null, 400);
      }

      // Check if token needs refresh
      const now = new Date();
      if (now >= connection.expiresAt && now < connection.refreshExpiresAt) {
        try {
          await this.authService.refreshToken(connectionId);
        } catch (refreshError) {
          return sendError(res, 'Failed to refresh token for testing', {
            error: refreshError instanceof Error ? refreshError.message : 'Unknown error'
          }, 500);
        }
      }

      return sendSuccess(res, 'Connection test completed successfully', {
        connection: {
          id: connection.id,
          realmId: connection.realmId,
          companyName: connection.companyName,
          isConnected: connection.isConnected,
          status: 'Connection is active and working'
        }
      });

    } catch (error) {
      console.error('Error testing connection:', error);
      return sendError(
        res,
        'Connection test failed',
        { error: error instanceof Error ? error.message : 'Unknown error' },
        500
      );
    }
  };
}