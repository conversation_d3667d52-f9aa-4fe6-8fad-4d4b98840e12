import { Request, Response, NextFunction } from 'express';

declare global {
  namespace Express {
    interface Request {
      qbAuth?: {
        accessToken: string;
        realmId: string;
      };
    }
  }
}

export const quickBooksAuthMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const accessToken = req.headers.authorization?.split(' ')[1];
  const realmId = req.headers['realm-id'] as string;

  if (!accessToken || !realmId) {
    res.status(400).json({ message: 'Missing access token or realm ID' });
    return;
  }

  req.qbAuth = { accessToken, realmId };
  next();
};
