// services/quickbooksAuthService.ts
import axios from 'axios';
import { PrismaClient } from '@prisma/client';
import { quickbooksConfig } from '../config/quickbooks';
import {
    QBOAuthTokenResponse,
    QBOTokenRefreshResponse,
    QBOAuthRequest,
    QBOConnectionData,
    QBOCompanyInfo,
    QBOErrorResponse,
    AuthState,
    QBOAuthUrl
} from '../types/quickbooks';
import { prisma } from '../config/db';


export class QuickBooksAuthService {
    private readonly CLIENT_ID = quickbooksConfig.clientId;
    private readonly CLIENT_SECRET = quickbooksConfig.clientSecret;
    private readonly REDIRECT_URI = quickbooksConfig.redirectUri;
    private readonly AUTH_URL = quickbooksConfig.authUrl;
    private readonly TOKEN_URL = quickbooksConfig.tokenUrl;
    private readonly API_BASE_URL = process.env.API_BASE_URL;

    /**
     * Generate QuickBooks OAuth authorization URL
     */
    public generateAuthUrl(returnUrl?: string): QBOAuthUrl {
        const state = this.generateState(returnUrl);
        const scopes = quickbooksConfig.scopes.join(' ');

        const authUrl = `${this.AUTH_URL}?` +
            `client_id=${this.CLIENT_ID}&` +
            `scope=${encodeURIComponent(scopes)}&` +
            `redirect_uri=${encodeURIComponent(this.REDIRECT_URI)}&` +
            `response_type=code&` +
            `access_type=offline&` +
            `state=${state}`;

        return { authUrl, state };
    }

    /**
     * Handle OAuth callback and exchange code for tokens
     */
    public async handleCallback(authRequest: QBOAuthRequest): Promise<QBOConnectionData> {
        try {
            console.log('Starting OAuth callback handling for realmId:', authRequest.realmId);

            // Validate state parameter
            this.validateState(authRequest.state);
            console.log('State validation successful');

            // Exchange authorization code for tokens
            const tokenResponse = await this.exchangeCodeForTokens(authRequest.code, authRequest.realmId);
            console.log('Token exchange successful');

            // Get company information
            if (!tokenResponse) {
                throw new Error('Failed to exchange code for tokens');
            }

            console.log('Fetching company information...');
            const companyInfo = await this.getCompanyInfo(tokenResponse.access_token, authRequest.realmId);
            console.log('Company info fetched successfully');

            if (!companyInfo) {
                throw new Error('Failed to fetch company information');
            }

            // Save or update connection in database
            console.log('Saving connection to database...');
            const connection = await this.saveConnection(tokenResponse, companyInfo);
            console.log('Connection saved successfully');

            return connection;
        } catch (error) {
            console.error('Error handling OAuth callback:', error);
            throw error instanceof Error ? error : new Error('Failed to handle OAuth callback');
        }
    }

    /**
     * Exchange authorization code for access and refresh tokens
     */
    private async exchangeCodeForTokens(code: string, realmId: string): Promise<QBOAuthTokenResponse> {
        try {
            const tokenPayload = new URLSearchParams({
                grant_type: 'authorization_code',
                code: code,
                redirect_uri: this.REDIRECT_URI
            });

            const authHeader = Buffer.from(`${this.CLIENT_ID}:${this.CLIENT_SECRET}`).toString('base64');

            console.log('Exchanging code for tokens with redirect URI:', this.REDIRECT_URI);

            const response = await axios.post<QBOAuthTokenResponse>(this.TOKEN_URL, tokenPayload, {
                headers: {
                    'Authorization': `Basic ${authHeader}`,
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                }
            });

            if (!response.data.access_token) {
                throw new Error('No access token received from QuickBooks');
            }

            return { ...response.data, realmId };
        } catch (error) {
            console.error('Error exchanging code for tokens:', error);
            if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                const data = error.response?.data;
                throw new Error(`Token exchange failed (${status}): ${JSON.stringify(data)}`);
            }
            throw new Error('Failed to exchange authorization code for tokens');
        }
    }

    /**
     * Get company information from QuickBooks
     */
    private async getCompanyInfo(accessToken: string, realmId: string): Promise<QBOCompanyInfo> {
        try {
            const response = await axios.get<QBOCompanyInfo>(
                `${this.API_BASE_URL}${realmId}/companyinfo/1`,
                {
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Accept': 'application/json'
                    }
                }
            );

            if (!response.data || !response.data.QueryResponse || !response.data.QueryResponse.CompanyInfo) {
                throw new Error('Invalid company info response from QuickBooks API');
            }

            return response.data;
        } catch (error) {
            console.error('Error fetching company info from QuickBooks:', error);
            if (axios.isAxiosError(error)) {
                const status = error.response?.status;
                const message = error.response?.data?.Fault?.Error?.[0]?.Detail || error.message;
                throw new Error(`QuickBooks API error (${status}): ${message}`);
            }
            throw new Error('Failed to fetch company information from QuickBooks');
        }
    }

    /**
     * Save connection to database
     */
    private async saveConnection(
        tokenResponse: QBOAuthTokenResponse,
        companyInfo: QBOCompanyInfo
    ): Promise<QBOConnectionData> {
        const now = new Date();
        const expiresAt = new Date(now.getTime() + (tokenResponse.expires_in * 1000));
        const refreshExpiresAt = new Date(now.getTime() + (tokenResponse.refresh_token_expires_in * 1000));

        const companyName = companyInfo.QueryResponse.CompanyInfo[0]?.Name || 'Unknown Company';

        const connection = await prisma.qBOConnection.upsert({
            where: { id: tokenResponse.realmId },
            update: {
                accessToken: tokenResponse.access_token,
                refreshToken: tokenResponse.refresh_token,
                expiresAt,
                refreshExpiresAt,
                isConnected: true,
                companyName,
                connectedAt: now,
                disconnectedAt: null
            },
            create: {
                id: tokenResponse.realmId,
                accessToken: tokenResponse.access_token,
                refreshToken: tokenResponse.refresh_token,
                realmId: tokenResponse.realmId,
                expiresAt,
                refreshExpiresAt,
                isConnected: true,
                companyName,
                connectedAt: now
            }
        });

        return {
            id: connection.id,
            accessToken: connection.accessToken,
            refreshToken: connection.refreshToken,
            realmId: connection.realmId,
            expiresAt: connection.expiresAt,
            refreshExpiresAt: connection.refreshExpiresAt,
            isConnected: connection.isConnected,
            companyName: connection.companyName || undefined,
            connectedAt: connection.connectedAt,
            lastSyncAt: connection.lastSyncAt || undefined
        };
    }

    /**
     * Refresh access token using refresh token
     */
    public async refreshToken(connectionId: string): Promise<QBOConnectionData> {
        try {
            const connection = await prisma.qBOConnection.findUnique({
                where: { id: connectionId }
            });

            if (!connection) {
                throw new Error('Connection not found');
            }

            if (!connection.isConnected) {
                throw new Error('Connection is not active');
            }

            const tokenPayload = new URLSearchParams({
                grant_type: 'refresh_token',
                refresh_token: connection.refreshToken
            });

            const authHeader = Buffer.from(`${this.CLIENT_ID}:${this.CLIENT_SECRET}`).toString('base64');

            const response = await axios.post<QBOTokenRefreshResponse>(this.TOKEN_URL, tokenPayload, {
                headers: {
                    'Authorization': `Basic ${authHeader}`,
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                }
            });

            const now = new Date();
            const expiresAt = new Date(now.getTime() + (response.data.expires_in * 1000));
            const refreshExpiresAt = new Date(now.getTime() + (response.data.refresh_token_expires_in * 1000));

            const updatedConnection = await prisma.qBOConnection.update({
                where: { id: connectionId },
                data: {
                    accessToken: response.data.access_token,
                    refreshToken: response.data.refresh_token,
                    expiresAt,
                    refreshExpiresAt
                }
            });

            return {
                id: updatedConnection.id,
                accessToken: updatedConnection.accessToken,
                refreshToken: updatedConnection.refreshToken,
                realmId: updatedConnection.realmId,
                expiresAt: updatedConnection.expiresAt,
                refreshExpiresAt: updatedConnection.refreshExpiresAt,
                isConnected: updatedConnection.isConnected,
                companyName: updatedConnection.companyName || undefined,
                connectedAt: updatedConnection.connectedAt,
                lastSyncAt: updatedConnection.lastSyncAt || undefined
            };
        } catch (error) {
            console.error('Error refreshing token:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to refresh token');
        }
    }

    /**
     * Revoke tokens and disconnect from QuickBooks
     */
    public async disconnect(connectionId: string): Promise<void> {
        try {
            const connection = await prisma.qBOConnection.findUnique({
                where: { id: connectionId }
            });

            if (!connection) {
                throw new Error('Connection not found');
            }

            // Revoke refresh token
            const revokePayload = new URLSearchParams({
                token: connection.refreshToken
            });

            const authHeader = Buffer.from(`${this.CLIENT_ID}:${this.CLIENT_SECRET}`).toString('base64');

            await axios.post('https://developer.api.intuit.com/v2/oauth2/tokens/revoke', revokePayload, {
                headers: {
                    'Authorization': `Basic ${authHeader}`,
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            // Update connection status in database
            await prisma.qBOConnection.update({
                where: { id: connectionId },
                data: {
                    isConnected: false,
                    disconnectedAt: new Date()
                }
            });

        } catch (error) {
            console.error('Error disconnecting from QuickBooks:', error);
            throw new Error(error instanceof Error ? error.message : 'Failed to disconnect from QuickBooks');
        }
    }

    /**
     * Get connection by ID
     */
    public async getConnection(connectionId: string): Promise<QBOConnectionData | null> {
        const connection = await prisma.qBOConnection.findUnique({
            where: { id: connectionId }
        });

        if (!connection) {
            return null;
        }

        return {
            id: connection.id,
            accessToken: connection.accessToken,
            refreshToken: connection.refreshToken,
            realmId: connection.realmId,
            expiresAt: connection.expiresAt,
            refreshExpiresAt: connection.refreshExpiresAt,
            isConnected: connection.isConnected,
            companyName: connection.companyName || undefined,
            connectedAt: connection.connectedAt,
            lastSyncAt: connection.lastSyncAt || undefined
        };
    }

    /**
     * Get all active connections
     */
    public async getActiveConnections(): Promise<QBOConnectionData[]> {
        const connections = await prisma.qBOConnection.findMany({
            where: { isConnected: true }
        });

        return connections.map(connection => ({
            id: connection.id,
            accessToken: connection.accessToken,
            refreshToken: connection.refreshToken,
            realmId: connection.realmId,
            expiresAt: connection.expiresAt,
            refreshExpiresAt: connection.refreshExpiresAt,
            isConnected: connection.isConnected,
            companyName: connection.companyName || undefined,
            connectedAt: connection.connectedAt,
            lastSyncAt: connection.lastSyncAt || undefined
        }));
    }

    /**
     * Generate state parameter for OAuth
     */
    private generateState(returnUrl?: string): string {
        const state: AuthState = {
            timestamp: Date.now(),
            returnUrl
        };
        return Buffer.from(JSON.stringify(state)).toString('base64url');
    }

    /**
     * Validate state parameter
     */
    private validateState(state: string): AuthState {
        try {
            const decoded = JSON.parse(Buffer.from(state, 'base64url').toString());
            const now = Date.now();
            const stateAge = now - decoded.timestamp;

            // State should not be older than 10 minutes
            if (stateAge > 10 * 60 * 1000) {
                throw new Error('State parameter has expired');
            }

            return decoded;
        } catch (error) {
            throw new Error('Invalid state parameter');
        }
    }
}