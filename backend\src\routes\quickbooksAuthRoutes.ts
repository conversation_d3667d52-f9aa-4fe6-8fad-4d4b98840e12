// routes/quickbooksAuthRoutes.ts

import { Router } from 'express';
import { Quick<PERSON>ooksAuthController } from '../controller/quickbooksAuthController';

const authRoutes = Router();
const authController = new QuickBooksAuthController();

// OAuth Flow Routes
/**
 * @route GET /api/auth/quickbooks/connect
 * @desc Initialize QuickBooks OAuth flow
 * @access Public
 * @query returnUrl (optional) - URL to redirect after successful connection
 */
authRoutes.get('/connect', authController.connect);

/**
 * @route GET /api/auth/quickbooks/callback
 * @desc Handle OAuth callback from QuickBooks
 * @access Public
 * @query code - Authorization code from QuickB<PERSON>s
 * @query state - State parameter for security
 * @query realmId - QuickBooks company ID
 */
authRoutes.get('/callback', authController.callback);

// Token Management Routes
/**
 * @route POST /api/auth/quickbooks/refresh
 * @desc Refresh access token using refresh token
 * @access Private
 * @body connectionId - ID of the connection to refresh
 */
authRoutes.post('/refresh', authController.refresh);

/**
 * @route POST /api/auth/quickbooks/disconnect
 * @desc Revoke tokens and disconnect from QuickBooks
 * @access Private
 * @body connectionId - ID of the connection to disconnect
 */
authRoutes.post('/disconnect', authController.disconnect);

// Connection Management Routes
/**
 * @route GET /api/auth/quickbooks/status/:connectionId
 * @desc Get connection status and token information
 * @access Private
 * @param connectionId - ID of the connection to check
 */
authRoutes.get('/status/:connectionId', authController.getConnectionStatus);

/**
 * @route GET /api/auth/quickbooks/connections
 * @desc Get all active QuickBooks connections
 * @access Private
 */
authRoutes.get('/connections', authController.getConnections);

/**
 * @route POST /api/auth/quickbooks/test/:connectionId
 * @desc Test connection by making a simple API call
 * @access Private
 * @param connectionId - ID of the connection to test
 */
authRoutes.post('/test/:connectionId', authController.testConnection);

export default authRoutes;